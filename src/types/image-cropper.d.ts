export interface CropData {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface CropperSettings {
  aspectRatio?: number;
  viewMode: number;
  dragMode: 'crop' | 'move' | 'none';
  autoCropArea: number;
  responsive: boolean;
  restore: boolean;
  guides: boolean;
  center: boolean;
  highlight: boolean;
  cropBoxMovable: boolean;
  cropBoxResizable: boolean;
  toggleDragModeOnDblclick: boolean;
}

export interface ImageCropperTexts {
  // 主要标题和设置
  cropSettings: string;

  // 按钮文案
  selectImage: string;
  downloadImage: string;
  reset: string;

  // 表单标签
  width: string;
  height: string;
  xCoordinate: string;
  yCoordinate: string;
  zoomControl: string;
  currentZoom: string;
  commonSizes: string;

  // 提示文案
  clickToSelectImage: string;
  supportedFormats: string;
  scrollToZoom: string;

  // 按钮提示
  zoomOut: string;
  zoomIn: string;
  oneToOne: string;
  fitToWindow: string;
}

export interface ImageCropperProps {
  className?: string;
  onCropChange?: (cropData: CropData) => void;
  onImageLoad?: (imageUrl: string) => void;
  texts: ImageCropperTexts;
}

export interface CropperControlsState {
  width: number;
  height: number;
  x: number;
  y: number;
}
