export interface CircleCropTexts {
  title: string;
  uploadImage: string;
  dragDropHint: string;
  supportedFormats: string;
  preview: string;
  downloadOptions: string;
  download: string;
  selectSize: string;
  processing: string;
  noImageSelected: string;
  cropArea: string;
  adjustCrop: string;
  reselectImage: string;
}

export interface SizePreset {
  label: string;
  size: number;
}

export interface CircleCropProps {
  className?: string;
  texts: CircleCropTexts;
  sizePresets?: SizePreset[];
}

export interface CropState {
  x: number;
  y: number;
  width: number;
  height: number;
}
