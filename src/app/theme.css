:root {
  --background: oklch(0.9711 0.0074 80.7211);
  --foreground: oklch(0.3000 0.0358 30.2042);
  --card: oklch(0.9711 0.0074 80.7211);
  --card-foreground: oklch(0.3000 0.0358 30.2042);
  --popover: oklch(0.9711 0.0074 80.7211);
  --popover-foreground: oklch(0.3000 0.0358 30.2042);
  --primary: oklch(0.5234 0.1347 144.1672);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9571 0.0210 147.6360);
  --secondary-foreground: oklch(0.4254 0.1159 144.3078);
  --muted: oklch(0.9370 0.0142 74.4218);
  --muted-foreground: oklch(0.4495 0.0486 39.2110);
  --accent: oklch(0.8952 0.0504 146.0366);
  --accent-foreground: oklch(0.4254 0.1159 144.3078);
  --destructive: oklch(0.5386 0.1937 26.7249);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8805 0.0208 74.6428);
  --input: oklch(0.8805 0.0208 74.6428);
  --ring: oklch(0.5234 0.1347 144.1672);
  --chart-1: oklch(0.6731 0.1624 144.2083);
  --chart-2: oklch(0.5752 0.1446 144.1813);
  --chart-3: oklch(0.5234 0.1347 144.1672);
  --chart-4: oklch(0.4254 0.1159 144.3078);
  --chart-5: oklch(0.2157 0.0453 145.7256);
  --sidebar: oklch(0.9370 0.0142 74.4218);
  --sidebar-foreground: oklch(0.3000 0.0358 30.2042);
  --sidebar-primary: oklch(0.5234 0.1347 144.1672);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8952 0.0504 146.0366);
  --sidebar-accent-foreground: oklch(0.4254 0.1159 144.3078);
  --sidebar-border: oklch(0.8805 0.0208 74.6428);
  --sidebar-ring: oklch(0.5234 0.1347 144.1672);
  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Source Code Pro, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2683 0.0279 150.7681);
  --foreground: oklch(0.9423 0.0097 72.6595);
  --card: oklch(0.3327 0.0271 146.9867);
  --card-foreground: oklch(0.9423 0.0097 72.6595);
  --popover: oklch(0.3327 0.0271 146.9867);
  --popover-foreground: oklch(0.9423 0.0097 72.6595);
  --primary: oklch(0.6731 0.1624 144.2083);
  --primary-foreground: oklch(0.2157 0.0453 145.7256);
  --secondary: oklch(0.3942 0.0265 142.9926);
  --secondary-foreground: oklch(0.8970 0.0166 142.5518);
  --muted: oklch(0.3327 0.0271 146.9867);
  --muted-foreground: oklch(0.8579 0.0174 76.0955);
  --accent: oklch(0.5752 0.1446 144.1813);
  --accent-foreground: oklch(0.9423 0.0097 72.6595);
  --destructive: oklch(0.5386 0.1937 26.7249);
  --destructive-foreground: oklch(0.9423 0.0097 72.6595);
  --border: oklch(0.3942 0.0265 142.9926);
  --input: oklch(0.3942 0.0265 142.9926);
  --ring: oklch(0.6731 0.1624 144.2083);
  --chart-1: oklch(0.7660 0.1179 145.2950);
  --chart-2: oklch(0.7185 0.1417 144.8887);
  --chart-3: oklch(0.6731 0.1624 144.2083);
  --chart-4: oklch(0.6291 0.1543 144.2031);
  --chart-5: oklch(0.5752 0.1446 144.1813);
  --sidebar: oklch(0.2683 0.0279 150.7681);
  --sidebar-foreground: oklch(0.9423 0.0097 72.6595);
  --sidebar-primary: oklch(0.6731 0.1624 144.2083);
  --sidebar-primary-foreground: oklch(0.2157 0.0453 145.7256);
  --sidebar-accent: oklch(0.5752 0.1446 144.1813);
  --sidebar-accent-foreground: oklch(0.9423 0.0097 72.6595);
  --sidebar-border: oklch(0.3942 0.0265 142.9926);
  --sidebar-ring: oklch(0.6731 0.1624 144.2083);
  --font-sans: Montserrat, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono: Source Code Pro, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}