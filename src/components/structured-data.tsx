import { LandingPage } from "@/types/pages/landing";

interface StructuredDataProps {
  page: LandingPage;
  locale: string;
  url: string;
}

export default function StructuredData({ page, locale, url }: StructuredDataProps) {
  // WebApplication Schema
  const webApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": page.hero?.title || "Crop Image",
    "description": page.hero?.description?.replace(/<br\s*\/?>/gi, ' ') || "Professional online image cropper for perfect photo editing",
    "url": url,
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "Image cropping and editing",
      "Multiple aspect ratios",
      "High quality output", 
      "No registration required",
      "100% secure and private",
      "Supports JPG, PNG, GIF, WebP formats"
    ],
    "screenshot": `${url}/imgs/screenshot.jpg`,
    "softwareVersion": "1.0",
    "author": {
      "@type": "Organization",
      "name": "Crop Image",
      "url": url
    }
  };

  // Organization Schema
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": page.footer?.brand?.title || "Crop Image",
    "description": page.footer?.brand?.description || "Professional crop image tool for perfect photo editing",
    "url": url,
    "logo": `${url}${page.footer?.brand?.logo?.src || "/logo.png"}`,
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": page.footer?.social?.items?.map(item => item.url).filter(Boolean) || []
  };

  // FAQ Schema (if FAQ data exists)
  const faqSchema = page.faq?.items ? {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": page.faq.items.map(item => ({
      "@type": "Question",
      "name": item.title,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.description
      }
    }))
  } : null;

  // BreadcrumbList Schema
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": url
      }
    ]
  };

  // Combine all schemas
  const allSchemas = [
    webApplicationSchema,
    organizationSchema,
    breadcrumbSchema,
    ...(faqSchema ? [faqSchema] : [])
  ];

  return (
    <>
      {allSchemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
    </>
  );
}
