"use client";

import React, { useState, useRef, useCallback } from 'react';
import <PERSON><PERSON><PERSON>, { ReactCropperElement } from 'react-cropper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Upload, Download, RotateCcw, Settings, ChevronUp, ChevronDown, ZoomIn, ZoomOut, Maximize } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ImageCropperProps, CropperControlsState, CropData } from '@/types/image-cropper';

export default function ImageCropper({ className, onCropChange, onImageLoad, texts }: ImageCropperProps) {
  const cropperRef = useRef<ReactCropperElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [image, setImage] = useState<string>('');
  const [originalFileType, setOriginalFileType] = useState<string>('image/png'); // 存储原始文件类型
  const [originalFileName, setOriginalFileName] = useState<string>('image'); // 存储原始文件名
  const [controls, setControls] = useState<CropperControlsState>({
    width: 200,
    height: 200,
    x: 0,
    y: 0
  });
  const [isControlsExpanded, setIsControlsExpanded] = useState<boolean>(true);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [showZoomIndicator, setShowZoomIndicator] = useState<boolean>(false);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);

  // 处理文件上传的通用函数
  const processFile = useCallback((file: File) => {
    if (file && file.type.startsWith('image/')) {
      // 保存原始文件信息
      setOriginalFileType(file.type);
      setOriginalFileName(file.name.split('.')[0] || 'image'); // 获取文件名（不含扩展名）

      const reader = new FileReader();
      reader.onload = () => {
        const imageUrl = reader.result as string;
        setImage(imageUrl);
        onImageLoad?.(imageUrl);
      };
      reader.readAsDataURL(file);
    }
  }, [onImageLoad]);

  // 处理文件输入框上传
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  }, [processFile]);

  // 处理拖拽事件
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // 只有当离开整个拖拽区域时才设置为false
    if (e.currentTarget === e.target) {
      setIsDragOver(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));

    if (imageFile) {
      processFile(imageFile);
    }
  }, [processFile]);

  // 处理裁剪数据变化
  const handleCropChange = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const cropData = cropper.getData();
      const newControls = {
        ...controls,
        width: Math.round(cropData.width),
        height: Math.round(cropData.height),
        x: Math.round(cropData.x),
        y: Math.round(cropData.y)
      };
      setControls(newControls);

      const cropInfo: CropData = {
        x: newControls.x,
        y: newControls.y,
        width: newControls.width,
        height: newControls.height
      };
      onCropChange?.(cropInfo);
    }
  }, [controls, onCropChange]);

  // 处理缩放事件
  const handleZoomEvent = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const imageData = cropper.getImageData();
      const currentZoom = imageData.naturalWidth > 0 ? imageData.width / imageData.naturalWidth : 1;
      setZoomLevel(currentZoom);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  // 处理控制面板数值变化
  const handleControlChange = useCallback((field: keyof CropperControlsState, value: number) => {
    const newControls = { ...controls, [field]: value };
    setControls(newControls);

    if (cropperRef.current?.cropper) {
      const cropper = cropperRef.current.cropper;
      cropper.setData({
        x: newControls.x,
        y: newControls.y,
        width: newControls.width,
        height: newControls.height
      });
    }
  }, [controls]);



  // 下载裁剪后的图片
  const handleDownload = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      const canvas = cropper.getCroppedCanvas({
        width: controls.width,
        height: controls.height,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
      });

      // 根据原始文件类型确定输出格式和文件扩展名
      const getFileExtension = (mimeType: string): string => {
        switch (mimeType) {
          case 'image/jpeg':
          case 'image/jpg':
            return 'jpg';
          case 'image/png':
            return 'png';
          case 'image/webp':
            return 'webp';
          case 'image/gif':
            return 'gif';
          case 'image/bmp':
            return 'bmp';
          default:
            return 'png'; // 默认为 PNG
        }
      };

      const fileExtension = getFileExtension(originalFileType);
      const fileName = `${originalFileName}-cropped-${Date.now()}.${fileExtension}`;

      // 根据文件类型设置质量参数
      const quality = originalFileType === 'image/jpeg' ? 0.9 : undefined;

      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = fileName;
          a.click();
          URL.revokeObjectURL(url);
        }
      }, originalFileType, quality);
    }
  }, [controls, originalFileType, originalFileName]);

  // 设置预设尺寸
  const handlePresetSize = useCallback((width: number, height: number) => {
    const newControls = { ...controls, width, height };
    setControls(newControls);
    if (cropperRef.current?.cropper) {
      cropperRef.current.cropper.setData({
        x: newControls.x,
        y: newControls.y,
        width: newControls.width,
        height: newControls.height
      });
    }
  }, [controls]);

  // 缩放控制
  const handleZoom = useCallback((delta: number) => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      cropper.zoom(delta);
      const imageData = cropper.getImageData();
      const currentZoom = imageData.naturalWidth > 0 ? imageData.width / imageData.naturalWidth : 1;
      setZoomLevel(currentZoom);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  const handleZoomTo = useCallback((zoom: number) => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      cropper.zoomTo(zoom);
      setZoomLevel(zoom);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  const handleFitToContainer = useCallback(() => {
    const cropper = cropperRef.current?.cropper;
    if (cropper) {
      cropper.reset();
      setZoomLevel(1);
      setShowZoomIndicator(true);
      setTimeout(() => setShowZoomIndicator(false), 1500);
    }
  }, []);

  // 重置
  const handleReset = useCallback(() => {
    if (cropperRef.current?.cropper) {
      cropperRef.current.cropper.reset();
    }
    setControls({
      width: 200,
      height: 200,
      x: 0,
      y: 0
    });
    setZoomLevel(1);
  }, []);

  return (
    <section className={cn("py-0", className)} id='crop-image'>
      <div className="container">
        {/* <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">图片裁剪工具</h2>
          <p className="text-muted-foreground">上传图片并进行精确裁剪</p>
        </div> */}
        
        {/* 响应式布局：移动端上下堆叠，桌面端左右分栏 */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* 移动端：图片预览区在上方 */}
          <div className="lg:col-span-8 lg:order-2 order-1">
            <Card className="h-full">
              <CardContent className="p-6">
                {image ? (
                  <div className="w-full relative h-[600px]">
                    <Cropper
                      ref={cropperRef}
                      src={image}
                      style={{ height: '100%', width: '100%' }}
                      initialAspectRatio={NaN}
                      guides={true}
                      crop={handleCropChange}
                      cropend={handleCropChange}
                      ready={handleCropChange}
                      zoom={handleZoomEvent}
                      viewMode={1}
                      dragMode="crop"
                      scalable={true}
                      cropBoxMovable={true}
                      cropBoxResizable={true}
                      toggleDragModeOnDblclick={false}
                      responsive={true}
                      restore={false}
                      center={true}
                      highlight={true}
                      autoCropArea={0.8}
                    />

                    {/* 缩放指示器 */}
                    {showZoomIndicator && (
                      <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-md text-sm font-medium transition-opacity duration-300">
                        {Math.round(zoomLevel * 100)}%
                      </div>
                    )}

                    {/* 缩放提示 */}
                    <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-md text-xs">
                      {texts.scrollToZoom}
                    </div>
                  </div>
                ) : (
                  <div
                    className={cn(
                      "flex items-center justify-center h-[600px] border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200",
                      isDragOver
                        ? "border-primary bg-primary/10 border-solid"
                        : "border-muted-foreground/25 hover:border-muted-foreground/50 hover:bg-muted/20"
                    )}
                    onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <div className="text-center pointer-events-none">
                      <Upload className={cn(
                        "w-12 h-12 mx-auto mb-4 transition-colors",
                        isDragOver ? "text-primary" : "text-muted-foreground"
                      )} />
                      <p className={cn(
                        "transition-colors",
                        isDragOver ? "text-primary font-medium" : "text-muted-foreground"
                      )}>
                        {isDragOver ? "松开以上传图片" : texts.clickToSelectImage}
                      </p>
                      <p className="text-xs text-muted-foreground/70 mt-2">
                        {texts.supportedFormats}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 控制面板 */}
          <div className="lg:col-span-4 lg:order-1 order-2">
            <Card className="h-full">
              <CardHeader>
                <CardTitle
                  className="flex items-center justify-between cursor-pointer lg:cursor-default"
                  onClick={() => setIsControlsExpanded(!isControlsExpanded)}
                >
                  <div className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    {texts.cropSettings}
                  </div>
                  <div className="lg:hidden">
                    {isControlsExpanded ? (
                      <ChevronUp className="w-5 h-5" />
                    ) : (
                      <ChevronDown className="w-5 h-5" />
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className={cn(
                "space-y-4 transition-all duration-300 flex-1 flex flex-col",
                !isControlsExpanded && "lg:flex hidden"
              )}>
                {/* 文件上传 */}
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    className="w-full"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {texts.selectImage}
                  </Button>
                </div>

                {/* 尺寸控制 */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-sm font-medium">{texts.width}</label>
                    <Input
                      type="number"
                      value={controls.width}
                      onChange={(e) => handleControlChange('width', parseInt(e.target.value) || 0)}
                      min="1"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">{texts.height}</label>
                    <Input
                      type="number"
                      value={controls.height}
                      onChange={(e) => handleControlChange('height', parseInt(e.target.value) || 0)}
                      min="1"
                    />
                  </div>
                </div>

                {/* 位置控制 */}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-sm font-medium">{texts.xCoordinate}</label>
                    <Input
                      type="number"
                      value={controls.x}
                      onChange={(e) => handleControlChange('x', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">{texts.yCoordinate}</label>
                    <Input
                      type="number"
                      value={controls.y}
                      onChange={(e) => handleControlChange('y', parseInt(e.target.value) || 0)}
                      min="0"
                    />
                  </div>
                </div>

                {/* 缩放控制 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">{texts.zoomControl}</label>
                  <div className="space-y-2">
                    {/* 缩放级别显示 */}
                    <div className="flex items-center justify-between text-sm">
                      <span>{texts.currentZoom}</span>
                      <span className="font-medium">{Math.round(zoomLevel * 100)}%</span>
                    </div>

                    {/* 缩放滑块 */}
                    <div className="flex items-center gap-2">
                      <ZoomOut className="w-4 h-4 text-muted-foreground" />
                      <input
                        type="range"
                        min="0.1"
                        max="5"
                        step="0.1"
                        value={zoomLevel}
                        onChange={(e) => handleZoomTo(parseFloat(e.target.value))}
                        disabled={!image}
                        className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
                      />
                      <ZoomIn className="w-4 h-4 text-muted-foreground" />
                    </div>

                    {/* 缩放按钮 */}
                    <div className="grid grid-cols-4 gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleZoom(-0.1)}
                        disabled={!image}
                        title={texts.zoomOut}
                      >
                        <ZoomOut className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleZoom(0.1)}
                        disabled={!image}
                        title={texts.zoomIn}
                      >
                        <ZoomIn className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleZoomTo(1)}
                        disabled={!image}
                        title={texts.oneToOne}
                      >
                        1:1
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleFitToContainer}
                        disabled={!image}
                        title={texts.fitToWindow}
                      >
                        <Maximize className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>



                {/* 预设尺寸 */}
                <div>
                  <label className="text-sm font-medium mb-2 block">{texts.commonSizes}</label>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(200, 200)}
                    >
                      200×200
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(400, 300)}
                    >
                      400×300
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(800, 600)}
                    >
                      800×600
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePresetSize(1920, 1080)}
                    >
                      1920×1080
                    </Button>
                  </div>
                </div>

                {/* 填充空间 */}
                <div className="flex-1"></div>

                {/* 操作按钮 */}
                <div className="space-y-2 mt-auto">
                  <Button
                    onClick={handleDownload}
                    disabled={!image}
                    className="w-full"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    {texts.downloadImage}
                  </Button>
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    disabled={!image}
                    className="w-full"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    {texts.reset}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>


        </div>
      </div>
    </section>
  );
}
