#!/bin/bash

# Vercel build script with error handling
set -e

echo "Starting Vercel build process..."

# Set environment variables
export NODE_OPTIONS="--max-old-space-size=6144 --no-warnings"
export NEXT_TELEMETRY_DISABLED=1
export CI=true

# Try normal build first
echo "Attempting normal build..."
if pnpm run build; then
    echo "Normal build succeeded!"
    exit 0
fi

echo "Normal build failed, trying simplified build..."
# If normal build fails, try simplified build
if pnpm run build:simple; then
    echo "Simplified build succeeded!"
    exit 0
fi

echo "Simplified build failed, trying clean build..."
# If simplified build fails, try clean build (no MDX)
if pnpm run build:clean; then
    echo "Clean build succeeded!"
    exit 0
fi

echo "All builds failed"
exit 1
